
import json
from imports.pg import pg_query_to_df



df = pg_query_to_df("""
    SELECT * 
    FROM information_schema.columns
    WHERE table_schema = 'public'
""")

df.head()

# https://github.com/tcv-eng/tcv-research/blob/9a3851d765983829a77c72e0fb494f6d2567feb1/2025/2025-04-evaluate-artemis-requests/const%20TABLES%20%3D%20%7B.ts
# Converted to JSON
with open('tables.json', 'r') as f:
    tables = json.load(f)

# Get all table names from the dataframe
df_tables = set(df['table_name'].unique())

# Get all table names from the tables dictionary
json_tables = set(tables.keys())

# Find tables that are in the JSON but not in the database
missing_tables = json_tables - df_tables

print("Tables in JSON but not found in database:")
for table in sorted(missing_tables):
    print(f"- {table}")

# Find tables that are in the database but not in the JSON
extra_tables = df_tables - json_tables

print("\nTables in database but not in JSON:")
for table in sorted(extra_tables):
    print(f"- {table}")


# For each table in the JSON, check if all its columns exist in the database
for table_name, table_info in tables.items():
    if 'columns' not in table_info:
        continue

    if table_name == 'deal_users':
        table_name = 'deals_users'
        
    # Get columns for this table from the database
    db_columns = set(df[df['table_name'] == table_name]['column_name'])
    
    # Get columns from JSON
    json_columns = set(table_info['columns'].keys())
    
    # Find columns in JSON but not in database
    missing_columns = json_columns - db_columns
    
    if missing_columns:
        print(f"\n`{table_name}`")
        for col in sorted(missing_columns):
            print(f"- `{col}`")
